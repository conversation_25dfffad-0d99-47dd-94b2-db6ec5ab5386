package com.xixun.modules.cat1.controller;

import com.xixun.common.constant.HttpStatusConstant;
import com.xixun.common.exception.DeviceApiException;
import com.xixun.common.exception.DeviceOperationException;
import com.xixun.common.utils.DeviceApiUtil;
import com.xixun.common.utils.R;
import com.xixun.modules.cat1.dto.DeviceReply;
import com.xixun.modules.cat1.dto.QueryParamsDTO;
import com.xixun.modules.cat1.dto.SetParamsDTO;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.HttpClientErrorException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/5/29 14:33
 * @Version 1.0
 */
@Slf4j
@RestController
@RequestMapping("/cat1/device/api")
public class Cat1DeviceApiController {

    private final DeviceApiUtil deviceApiUtil;

    @Autowired
    public Cat1DeviceApiController(DeviceApiUtil deviceApiUtil) {
        this.deviceApiUtil = deviceApiUtil;
    }


    /**
     * 示例：获取单个设备的计量数据并处理结果，返回 CompletableFuture<R>。
     * @param deviceName 设备名称
     * @return CompletableFuture<R> 包含操作结果的R对象
     */
    @GetMapping("/baseInfo/{deviceName}")
    public CompletableFuture<R> processMeteringDataForDevice(@PathVariable String deviceName) {
        log.info("开始获取设备 {} 的计量数据...", deviceName);
        CompletableFuture<DeviceReply> futureReply = deviceApiUtil.getMeteringData(deviceName);

        return futureReply.thenApply(reply -> {
            // 此处 handleDeviceResponse 确保了如果到这里，reply.getCo() 一定是 0
            log.info("成功获取设备 {} 的计量数据: {}", deviceName, reply);
            // 将 DeviceReply 封装到 R.ok() 中
            // 你可以决定如何将 reply 的内容放入 R 对象
            // 例如，直接放入整个 reply，或者只放入 reply.getDa()
            return R.ok().put("data", reply);
        }).exceptionally(ex -> handleSingleException(ex,deviceName,"查询计量数据"));
    }


    @PostMapping("/common/query")
    public CompletableFuture<R> queryCommonCmd(@RequestBody List<QueryParamsDTO> list) {

        CompletableFuture<Map<String, DeviceReply>> futureReply = deviceApiUtil.batchCommonQueryCmd(list);

        return futureReply.thenApply(replyMap -> {
            // 转换数据格式：将 Map 转换为 List，符合期望的响应格式
            List<Map<String, Object>> resultList = list.stream()
                    .map(queryParam -> {
                        String deviceName = queryParam.getDeviceName();
                        String commandKey = deviceName + ":" + queryParam.getIdi();

                        Map<String, Object> resultItem = new HashMap<>();
                        resultItem.put("deviceId", deviceName);

                        DeviceReply reply = replyMap.get(commandKey);
                        if (reply != null) {
                            // 成功的情况
                            resultItem.put("_type", "success");
                            resultItem.put("code", reply.getCo());
                            resultItem.put("msg", reply.getMe() != null ? reply.getMe() : "操作成功");
                            resultItem.put("data", reply.getDa());
                            resultItem.put("id", reply.getId());
                            resultItem.put("ve", reply.getVe());
                        } else {
                            // 失败的情况（设备未在结果Map中出现）
                            resultItem.put("_type", "error");
                            resultItem.put("code", 1);
                            resultItem.put("msg", "设备查询失败或设备端操作不成功");
                        }

                        return resultItem;
                    })
                    .collect(Collectors.toList());

            return R.ok().put("data", resultList);

        }).exceptionally(ex ->  handleBatchException(ex, "批量查询"));
    }

    /**
     * 批量设置通用命令
     */
    @PostMapping("/common/set")
    public CompletableFuture<R> setCommonCmd(@RequestBody List<SetParamsDTO> list) {

        CompletableFuture<Map<String, DeviceReply>> futureReply = deviceApiUtil.batchCommonSetCmdEnhanced(list);

        return futureReply.thenApply(replyMap -> {
            List<Map<String, Object>> resultList = list.stream()
                    .map(setParam -> {
                        String deviceName = setParam.getDeviceName();
                        String commandKey = deviceName + ":" + setParam.getIdi();

                        Map<String, Object> resultItem = new HashMap<>();
                        resultItem.put("deviceId", deviceName);
                        resultItem.put("idi", setParam.getIdi());

                        DeviceReply reply = replyMap.get(commandKey);
                        if (reply != null) {
                            resultItem.put("_type", "success");
                            resultItem.put("code", reply.getCo());
                            resultItem.put("msg", reply.getMe() != null ? reply.getMe() : "设置成功");
                            resultItem.put("data", reply.getDa());
                            resultItem.put("id", reply.getId());
                            resultItem.put("ve", reply.getVe());
                        } else {
                            resultItem.put("_type", "error");
                            resultItem.put("code", 1);
                            resultItem.put("msg", "设备设置失败或设备端操作不成功");
                        }

                        return resultItem;
                    })
                    .collect(Collectors.toList());

            return R.ok().put("data", resultList);

        }).exceptionally(ex -> handleBatchException(ex, "批量设置"));
    }


    /**
     * 统一处理批量操作的异常
     */
    private R handleBatchException(Throwable ex, String operation) {
        log.error("{}时发生错误: {}", operation, ex.getMessage(), ex);

        if (ex.getCause() instanceof DeviceOperationException) {
            DeviceOperationException opEx = (DeviceOperationException) ex.getCause();
            log.error("设备端操作失败详情 - 代码: {}, 消息: '{}', 完整回复: {}",
                    opEx.getDeviceCode(), opEx.getDeviceMessage(), opEx.getReply());
            return R.error(opEx.getDeviceCode(), opEx.getDeviceMessage()).put("details", opEx.getReply());
        } else if (ex.getCause() instanceof DeviceApiException) {
            DeviceApiException apiEx = (DeviceApiException) ex.getCause();
            if (apiEx.getCause() instanceof HttpClientErrorException) {
                HttpClientErrorException httpEx = (HttpClientErrorException) apiEx.getCause();
                log.error("HTTP客户端错误: {}, 响应体: {}", httpEx.getStatusCode(), httpEx.getResponseBodyAsString());
                return R.error(httpEx.getStatusCode().value(), "HTTP Error: " + httpEx.getResponseBodyAsString());
            } else if (apiEx.getErrorReply() != null) {
                DeviceReply errorReplyBody = apiEx.getErrorReply();
                return R.error(errorReplyBody.getCo(), errorReplyBody.getMe()).put("details", errorReplyBody.getDa());
            }
            return R.error(HttpStatusConstant.ERROR, apiEx.getMessage());
        } else {
            String errorMessage = (ex.getMessage() != null) ? ex.getMessage() : "未知错误，请联系管理员";
            if (ex.getCause() != null && ex.getCause().getMessage() != null) {
                errorMessage = ex.getCause().getMessage();
            }
            return R.error(HttpStatusConstant.ERROR, errorMessage + " (" + ex.getClass().getSimpleName() + ")");
        }
    }


    /**
     * 统一处理单个操作的异常
     */
    private R handleSingleException(Throwable ex, String deviceName, String operation) {
        log.error("设备 {} {}时发生错误: {}", deviceName, operation, ex.getMessage());

        if (ex.getCause() instanceof DeviceOperationException) {
            DeviceOperationException opEx = (DeviceOperationException) ex.getCause();
            log.error("设备端操作失败详情 - 代码: {}, 消息: '{}', 完整回复: {}",
                    opEx.getDeviceCode(), opEx.getDeviceMessage(), opEx.getReply());
            return R.error(opEx.getDeviceCode(), opEx.getDeviceMessage()).put("details", opEx.getReply());
        } else if (ex.getCause() instanceof DeviceApiException) {
            DeviceApiException apiEx = (DeviceApiException) ex.getCause();
            if (apiEx.getCause() instanceof HttpClientErrorException) {
                HttpClientErrorException httpEx = (HttpClientErrorException) apiEx.getCause();
                log.error("HTTP客户端错误: {}, 响应体: {}", httpEx.getStatusCode(), httpEx.getResponseBodyAsString());
                return R.error(httpEx.getStatusCode().value(), "HTTP Error: " + httpEx.getResponseBodyAsString());
            } else if (apiEx.getErrorReply() != null) {
                DeviceReply errorReplyBody = apiEx.getErrorReply();
                return R.error(errorReplyBody.getCo(), errorReplyBody.getMe()).put("details", errorReplyBody.getDa());
            }
            return R.error(HttpStatusConstant.ERROR, apiEx.getMessage());
        } else {
            String errorMessage = (ex.getMessage() != null) ? ex.getMessage() : "未知错误，请联系管理员";
            if (ex.getCause() != null && ex.getCause().getMessage() != null) {
                errorMessage = ex.getCause().getMessage();
            }
            return R.error(HttpStatusConstant.ERROR, errorMessage + " (" + ex.getClass().getSimpleName() + ")");
        }
    }


}
