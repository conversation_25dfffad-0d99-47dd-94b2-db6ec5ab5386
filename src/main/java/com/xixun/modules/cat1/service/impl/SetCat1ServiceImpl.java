package com.xixun.modules.cat1.service.impl;

import com.xixun.common.constant.CmdConstant;
import com.xixun.common.utils.*;
import com.xixun.modules.cat1.entity.Cat1Schedules;
import com.xixun.modules.cat1.service.Cat1SchedulesService;
import com.xixun.modules.cat1.service.SetCat1Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/7/14 14:57
 * @Version 1.0
 */
@Service("setCat1Service")
public class SetCat1ServiceImpl implements SetCat1Service {


    @Autowired
    private Cat1SchedulesService cat1SchedulesService;

    @Override
    public R setCat1Brightness(List<String> deviceIds, Integer brightness,Integer isOpen,Long userId) {
        int value=0;
        //开则设置亮度,否则亮度为0
        if (isOpen==1){
            // 将百分比转换成对应的亮度值
            // value= (int) ((brightness/100.0)*255);
            value=brightness;
        }
        ArrayList<String> hexList = new ArrayList<>();
        // 将整数转换为两位的十六进制字符串
        String hexString = String.format("%02X", value);
        hexList.add(hexString);
        hexList.add(hexString);
        //时长默认为1440分钟，即1天
        hexList.add("A0");
        hexList.add("05");
        List<Map> rs = ToCat1Util.batchSendPost(new SendCat1CmdArgs().setCmd("58").setDeviceIds(deviceIds).setUserId(userId)
                .setOperation("设置亮度").setData(hexList));
        if (rs.size()>0){
            return R.ok().put(CmdConstant.DATA, rs);
        }else {
            return R.error(LocaleUtils.getMessage("cat1.device.set_brightness_fail"));
        }
    }

    //@Override
    //public R setCat1Schedule(List<String> deviceIds, Long scheduleId, Long userId) {
    //
    //    //设置定时日表
    //    // setCat1ScheduleModelPrimary(deviceIds,userId);
    //    // setCat1ScheduleModelSecondary(deviceIds,userId);
    //
    //    Cat1Schedules schedules = cat1SchedulesService.getById(scheduleId);
    //    String[] brightnessArr = schedules.getBrightness().split(",");
    //    String[] colorTemperatureArr = schedules.getColorTemperature().split(",");
    //    String[] timeArr = schedules.getTimes().split(",");
    //    LinkedList<String> light1 = new LinkedList<>();
    //    LinkedList<String> light2 = new LinkedList<>();
    //
    //    // hexList.add("01");
    //    light1.add("01");
    //    light2.add("02");
    //    for (int i = 0; i < 12; i+=2) {
    //
    //        light1.add("01");
    //        light2.add("01");
    //        light1.add(String.format("%02X", Integer.parseInt(brightnessArr[i])));
    //        light2.add(String.format("%02X", Integer.parseInt(brightnessArr[i+1])));
    //        light1.add(String.format("%02X", Integer.parseInt(colorTemperatureArr[i])));
    //        light2.add(String.format("%02X", Integer.parseInt(colorTemperatureArr[i+1])));
    //
    //        String time=timeArr[i/2];
    //        String[] times = time.split(":");
    //        light1.add(String.format("%02X", Integer.parseInt(times[1])));
    //        light1.add(String.format("%02X", Integer.parseInt(times[0])));
    //        light2.add(String.format("%02X", Integer.parseInt(times[1])));
    //        light2.add(String.format("%02X", Integer.parseInt(times[0])));
    //
    //        // hexList.add("01");
    //        // String brightnessString = String.format("%02X", Integer.parseInt(brightnessArr[i]));
    //        // hexList.add(brightnessString);
    //        // String colorTemperatureString = String.format("%02X", Integer.parseInt(colorTemperatureArr[i]));
    //        // hexList.add(colorTemperatureString);
    //
    //        // String time=timeArr[i%6];
    //        // String[] times = time.split(":");
    //        //起始分钟
    //        // String minString = String.format("%02X", Integer.parseInt(times[1]));
    //        // hexList.add(minString);
    //        // //起始时钟
    //        // String hourString = String.format("%02X", Integer.parseInt(times[0]));
    //        // hexList.add(hourString);
    //        // if (i==5){
    //        //     hexList.add("02");
    //        // }
    //    }
    //    light1.addAll(light2);
    //    System.out.println(light1);
    //    List<Map> rs = ToCat1Util.batchSendPost(new SendCat1CmdArgs().setCmd("46").setDeviceIds(deviceIds).setUserId(userId)
    //            .setOperation("设置定时任务").setData(light1));
    //    return R.ok().put(CmdConstant.DATA, rs);
    //}

    @Override
    public R setCat1Schedule(List<String> deviceIds, Long scheduleId, Long userId) {
        Cat1Schedules schedules = cat1SchedulesService.getById(scheduleId);
        if (null==schedules){
            return R.error(LocaleUtils.getMessage("schedules.Please_select_a_timing"));
        }


        String[] brightnessArr = schedules.getBrightness().split(",");
        String[] colorTemperatureArr = schedules.getColorTemperature().split(",");
        String[] timeArr = schedules.getTimes().split(",");
        String[] linkedBrightnessArr = schedules.getLinkedBrightness().split(",");




        return R.ok().put(CmdConstant.DATA, null);
    }

    @Override
    public R setCat1ScheduleModelPrimary(List<String> deviceIds, Long userId) {
        ArrayList<String> hexList = new ArrayList<>();
        hexList.add("01");
        List<Map> rs = ToCat1Util.batchSendPost(new SendCat1CmdArgs().setCmd("47").setDeviceIds(deviceIds).setUserId(userId)
                .setOperation("设置日表模式(主灯)").setData(hexList));
        return R.ok().put(CmdConstant.DATA, rs);
    }

    @Override
    public R setCat1ScheduleModelSecondary(List<String> deviceIds, Long userId) {
        ArrayList<String> hexList = new ArrayList<>();
        hexList.add("01");
        List<Map> rs = ToCat1Util.batchSendPost(new SendCat1CmdArgs().setCmd("A1").setDeviceIds(deviceIds).setUserId(userId)
                .setOperation("设置日表模式(副灯)").setData(hexList));
        return R.ok().put(CmdConstant.DATA, rs);
    }


}
